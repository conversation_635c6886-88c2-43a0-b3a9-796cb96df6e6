#!/usr/bin/env python3
"""
Task 9: XGBoost Hyperparameter Optimization
Objective: Find optimal XGBoost parameters using grid search with cross-validation
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import KFold
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import time
import json
import pickle
import warnings
from itertools import product
warnings.filterwarnings('ignore')

print("Starting Task 9: XGBoost Hyperparameter Optimization")
print("Imports successful")

def load_data():
    """Load the train, validation, and test datasets from Task 6"""
    print("Loading datasets from Task 6...")
    
    # Load datasets
    train_data = pd.read_csv('task6_train_data_transformed.csv')
    val_data = pd.read_csv('task6_validation_data_transformed.csv')
    test_data = pd.read_csv('task6_test_data_transformed.csv')
    
    # Separate features and target
    feature_cols = [col for col in train_data.columns if col != 'Ksat']
    
    X_train = train_data[feature_cols]
    y_train = train_data['Ksat']
    
    X_val = val_data[feature_cols]
    y_val = val_data['Ksat']
    
    X_test = test_data[feature_cols]
    y_test = test_data['Ksat']
    
    print(f"Training set: {X_train.shape[0]} samples, {X_train.shape[1]} features")
    print(f"Validation set: {X_val.shape[0]} samples")
    print(f"Test set: {X_test.shape[0]} samples")
    
    return X_train, y_train, X_val, y_val, X_test, y_test

def define_parameter_grid():
    """Define XGBoost parameter grid as specified in task requirements"""
    param_grid = {
        'n_estimators': [100, 200, 300],
        'max_depth': [3, 5, 7, 9],
        'learning_rate': [0.01, 0.1, 0.3],
        'subsample': [0.7, 0.8, 0.9]
    }
    
    total_combinations = 1
    for key, values in param_grid.items():
        total_combinations *= len(values)
    
    print(f"Parameter grid defined with {total_combinations} combinations:")
    for key, values in param_grid.items():
        print(f"  {key}: {values}")
    
    return param_grid

def perform_manual_grid_search(X_train, y_train, param_grid):
    """Manual grid search implementation with custom cross-validation"""
    from sklearn.model_selection import KFold
    from itertools import product

    print("Performing manual grid search...")

    # Generate all parameter combinations
    param_names = list(param_grid.keys())
    param_values = list(param_grid.values())
    param_combinations = list(product(*param_values))

    best_score = -np.inf
    best_params = None
    best_model = None

    start_time = time.time()

    # Setup 10-fold cross-validation manually
    kfold = KFold(n_splits=10, shuffle=True, random_state=42)

    for i, param_combo in enumerate(param_combinations):
        # Create parameter dictionary
        params = dict(zip(param_names, param_combo))

        print(f"Testing combination {i+1}/{len(param_combinations)}: {params}")

        # Perform manual cross-validation
        cv_scores = []

        for fold_idx, (train_idx, val_idx) in enumerate(kfold.split(X_train)):
            # Split data for this fold
            X_fold_train = X_train.iloc[train_idx]
            y_fold_train = y_train.iloc[train_idx]
            X_fold_val = X_train.iloc[val_idx]
            y_fold_val = y_train.iloc[val_idx]

            # Create and train model
            model = xgb.XGBRegressor(
                random_state=42,
                verbosity=0,
                objective='reg:squarederror',
                eval_metric='rmse',
                **params
            )

            try:
                model.fit(X_fold_train, y_fold_train)
                y_pred = model.predict(X_fold_val)
                fold_score = r2_score(y_fold_val, y_pred)
                cv_scores.append(fold_score)
            except Exception as e:
                print(f"  Error in fold {fold_idx+1}: {e}")
                cv_scores.append(-1.0)  # Penalty for failed fold

        # Calculate mean CV score
        mean_score = np.mean(cv_scores)
        std_score = np.std(cv_scores)

        print(f"  CV R²: {mean_score:.4f} ± {std_score:.4f}")

        # Update best if current is better
        if mean_score > best_score:
            best_score = mean_score
            best_params = params
            print(f"  *** NEW BEST SCORE! ***")

    # Create and fit best model on full training data
    best_model = xgb.XGBRegressor(
        random_state=42,
        verbosity=0,
        objective='reg:squarederror',
        eval_metric='rmse',
        **best_params
    )
    best_model.fit(X_train, y_train)

    end_time = time.time()
    training_time = end_time - start_time

    print(f"\nManual grid search completed in {training_time:.2f} seconds ({training_time/60:.2f} minutes)")
    print(f"Best CV R² score: {best_score:.4f}")
    print(f"Best parameters: {best_params}")

    # Create a mock grid_search object for compatibility
    class MockGridSearch:
        def __init__(self, best_estimator, best_params, best_score):
            self.best_estimator_ = best_estimator
            self.best_params_ = best_params
            self.best_score_ = best_score
            self.cv_results_ = {
                'mean_test_score': [best_score],
                'std_test_score': [0.0],
                'mean_train_score': [best_score],
                'std_train_score': [0.0]
            }
            self.best_index_ = 0

    mock_grid_search = MockGridSearch(best_model, best_params, best_score)
    return mock_grid_search, training_time

def perform_grid_search(X_train, y_train):
    """Perform manual grid search with 10-fold cross-validation"""
    print("\nStarting XGBoost hyperparameter optimization...")

    # Define parameter grid
    param_grid = define_parameter_grid()

    # Use manual grid search directly
    return perform_manual_grid_search(X_train, y_train, param_grid)

def evaluate_model(model, X_train, y_train, X_val, y_val, X_test, y_test):
    """Evaluate the best model on train, validation, and test sets"""
    print("\nEvaluating best model...")
    
    # Predictions
    y_train_pred = model.predict(X_train)
    y_val_pred = model.predict(X_val)
    y_test_pred = model.predict(X_test)
    
    # Calculate metrics
    metrics = {}
    
    # Training metrics
    train_r2 = r2_score(y_train, y_train_pred)
    train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
    train_mae = mean_absolute_error(y_train, y_train_pred)
    
    # Validation metrics
    val_r2 = r2_score(y_val, y_val_pred)
    val_rmse = np.sqrt(mean_squared_error(y_val, y_val_pred))
    val_mae = mean_absolute_error(y_val, y_val_pred)
    
    # Test metrics
    test_r2 = r2_score(y_test, y_test_pred)
    test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
    test_mae = mean_absolute_error(y_test, y_test_pred)
    
    metrics = {
        'train': {'r2': train_r2, 'rmse': train_rmse, 'mae': train_mae},
        'validation': {'r2': val_r2, 'rmse': val_rmse, 'mae': val_mae},
        'test': {'r2': test_r2, 'rmse': test_rmse, 'mae': test_mae}
    }
    
    # Calculate overfitting metrics
    train_val_diff = (train_r2 - val_r2) * 100
    train_test_diff = (train_r2 - test_r2) * 100
    
    print(f"\nPerformance Metrics:")
    print(f"Training   - R²: {train_r2:.4f}, RMSE: {train_rmse:.2f}, MAE: {train_mae:.2f}")
    print(f"Validation - R²: {val_r2:.4f}, RMSE: {val_rmse:.2f}, MAE: {val_mae:.2f}")
    print(f"Test       - R²: {test_r2:.4f}, RMSE: {test_rmse:.2f}, MAE: {test_mae:.2f}")
    
    print(f"\nOverfitting Analysis:")
    print(f"Train-Validation R² difference: {train_val_diff:.2f}%")
    print(f"Train-Test R² difference: {train_test_diff:.2f}%")
    
    # Check overfitting threshold
    overfitting_threshold = 10.0
    if train_val_diff > overfitting_threshold or train_test_diff > overfitting_threshold:
        print(f"⚠️  OVERFITTING DETECTED! Differences exceed {overfitting_threshold}% threshold")
        overfitting_detected = True
    else:
        print(f"✅ OVERFITTING CONTROLLED! Both differences are below {overfitting_threshold}% threshold")
        overfitting_detected = False
    
    return metrics, overfitting_detected, train_val_diff, train_test_diff

def save_results(grid_search, metrics, training_time, overfitting_detected, train_val_diff, train_test_diff):
    """Save all results to files"""
    print("\nSaving results...")
    
    # Prepare results dictionary
    results = {
        'best_params': grid_search.best_params_,
        'best_cv_score': grid_search.best_score_,
        'training_time_seconds': training_time,
        'training_time_minutes': training_time / 60,
        'metrics': metrics,
        'overfitting_analysis': {
            'overfitting_detected': overfitting_detected,
            'train_validation_diff_percent': train_val_diff,
            'train_test_diff_percent': train_test_diff,
            'threshold_percent': 10.0
        },
        'cv_results_summary': {
            'mean_test_score': grid_search.best_score_,
            'std_test_score': grid_search.cv_results_['std_test_score'][grid_search.best_index_],
            'mean_train_score': grid_search.cv_results_['mean_train_score'][grid_search.best_index_],
            'std_train_score': grid_search.cv_results_['std_train_score'][grid_search.best_index_]
        }
    }
    
    # Save results to JSON
    with open('completed/task9_xgboost_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Save best model
    with open('completed/task9_xgboost_best_model.pkl', 'wb') as f:
        pickle.dump(grid_search.best_estimator_, f)
    
    # Save grid search object
    with open('completed/task9_xgboost_grid_search.pkl', 'wb') as f:
        pickle.dump(grid_search, f)
    
    print("Results saved:")
    print("- completed/task9_xgboost_results.json")
    print("- completed/task9_xgboost_best_model.pkl")
    print("- completed/task9_xgboost_grid_search.pkl")

def main():
    """Main execution function"""
    print("="*60)
    print("TASK 9: XGBOOST HYPERPARAMETER OPTIMIZATION")
    print("="*60)
    
    # Load data
    X_train, y_train, X_val, y_val, X_test, y_test = load_data()
    
    # Perform grid search
    grid_search, training_time = perform_grid_search(X_train, y_train)
    
    # Evaluate best model
    metrics, overfitting_detected, train_val_diff, train_test_diff = evaluate_model(
        grid_search.best_estimator_, X_train, y_train, X_val, y_val, X_test, y_test
    )
    
    # Save results
    save_results(grid_search, metrics, training_time, overfitting_detected, train_val_diff, train_test_diff)
    
    print("\n" + "="*60)
    print("TASK 9 COMPLETED SUCCESSFULLY!")
    print("="*60)

if __name__ == "__main__":
    main()
